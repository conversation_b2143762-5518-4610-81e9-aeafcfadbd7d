/**
 * 冲突解决对话框组件
 */
import React, { useState } from 'react';
import {
  Modal,
  Button,
  Radio,
  Space,
  Divider,
  Typography,
  Tabs,
  Card,
  Row,
  Col,
  Alert,
  Table,
  Tag,
  Form,
  Input
} from 'antd';
import {
  MergeCellsOutlined,
  UserOutlined,
  CloudOutlined,
  CheckCircleOutlined,

  EditOutlined,
  RobotOutlined,
  BulbOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import {
  Conflict,
  ConflictType,
  ConflictResolutionStrategy,
  conflictResolutionService
} from '../../services/ConflictResolutionService';
import { Operation, OperationType } from '../../services/CollaborationService';
import JsonView from '../common/JsonView';

const { Title, Text, Paragraph } = Typography;

// 组件属性
interface ConflictResolutionDialogProps {
  visible: boolean;
  conflict: Conflict;
  onClose: () => void;
}

// 获取操作类型名称
const getOperationTypeName = (type: OperationType, t: any): string => {
  switch (type) {
    case OperationType.ENTITY_CREATE:
      return t('collaboration.operations.entityCreate');
    case OperationType.ENTITY_UPDATE:
      return t('collaboration.operations.entityUpdate');
    case OperationType.ENTITY_DELETE:
      return t('collaboration.operations.entityDelete');
    case OperationType.COMPONENT_ADD:
      return t('collaboration.operations.componentAdd');
    case OperationType.COMPONENT_UPDATE:
      return t('collaboration.operations.componentUpdate');
    case OperationType.COMPONENT_REMOVE:
      return t('collaboration.operations.componentRemove');
    case OperationType.SCENE_UPDATE:
      return t('collaboration.operations.sceneUpdate');
    case OperationType.CURSOR_MOVE:
      return t('collaboration.operations.cursorMove');
    case OperationType.SELECTION_CHANGE:
      return t('collaboration.operations.selectionChange');
    default:
      return t('collaboration.operationDescriptions.unknown');
  }
};

// 获取操作描述
const getOperationDescription = (operation: Operation, t: any): string => {
  switch (operation.type) {
    case OperationType.ENTITY_CREATE:
      return t('collaboration.operationDescriptions.entityCreate', {
        name: operation.data.name || operation.data.id
      });

    case OperationType.ENTITY_UPDATE:
      return t('collaboration.operationDescriptions.entityUpdate', {
        name: operation.data.name || operation.data.id
      });

    case OperationType.ENTITY_DELETE:
      return t('collaboration.operationDescriptions.entityDelete', {
        name: operation.data.name || operation.data.id
      });

    case OperationType.COMPONENT_ADD:
      return t('collaboration.operationDescriptions.componentAdd', {
        entity: operation.data.entityName || operation.data.entityId,
        component: operation.data.componentType
      });

    case OperationType.COMPONENT_UPDATE:
      return t('collaboration.operationDescriptions.componentUpdate', {
        entity: operation.data.entityName || operation.data.entityId,
        component: operation.data.componentType
      });

    case OperationType.COMPONENT_REMOVE:
      return t('collaboration.operationDescriptions.componentRemove', {
        entity: operation.data.entityName || operation.data.entityId,
        component: operation.data.componentType
      });

    case OperationType.SCENE_UPDATE:
      return t('collaboration.operationDescriptions.sceneUpdate');

    case OperationType.CURSOR_MOVE:
      return t('collaboration.operationDescriptions.cursorMove');

    case OperationType.SELECTION_CHANGE:
      const count = operation.data.selectedIds?.length || 0;
      return t('collaboration.operationDescriptions.selectionChange', { count });

    default:
      return t('collaboration.operationDescriptions.unknown');
  }
};

/**
 * 冲突解决对话框组件
 */
const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  visible,
  conflict,
  onClose
}) => {
  const { t } = useTranslation();

  // 状态
  const [resolutionStrategy, setResolutionStrategy] = useState<ConflictResolutionStrategy>(
    ConflictResolutionStrategy.MERGE
  );
  const [customResolution, setCustomResolution] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[]>([]);
  const [aiLoading, setAiLoading] = useState(false);
  const [showAiSuggestions, setShowAiSuggestions] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  // 处理解决策略变更
  const handleStrategyChange = (e: any) => {
    setResolutionStrategy(e.target.value);
  };

  // 处理确认
  const handleConfirm = () => {
    setLoading(true);

    // 解决冲突
    conflictResolutionService.resolveConflict(
      conflict.id,
      resolutionStrategy,
      customResolution
    );

    setLoading(false);
    onClose();
  };

  // 处理取消
  const handleCancel = React.useCallback(() => {
    try {
      console.log('ConflictResolutionDialog: 开始关闭对话框');

      // 重置状态
      setResolutionStrategy(ConflictResolutionStrategy.MERGE);
      setCustomResolution(null);
      setAiSuggestions([]);
      setShowAiSuggestions(false);
      setFeedbackSubmitted(false);
      setLoading(false);
      setAiLoading(false);

      console.log('ConflictResolutionDialog: 状态已重置，调用关闭回调');

      // 调用关闭回调
      if (typeof onClose === 'function') {
        onClose();
      } else {
        console.error('ConflictResolutionDialog: onClose 不是一个函数');
      }
    } catch (error) {
      console.error('ConflictResolutionDialog: 关闭对话框失败:', error);
      // 即使出错也要尝试关闭
      try {
        if (typeof onClose === 'function') {
          onClose();
        }
      } catch (fallbackError) {
        console.error('ConflictResolutionDialog: 备用关闭也失败:', fallbackError);
      }
    }
  }, [onClose]);

  // 加载AI建议
  const loadAiSuggestions = async () => {
    setAiLoading(true);
    setShowAiSuggestions(true);

    try {
      const suggestions = await conflictResolutionService.getAISuggestions(conflict.id);
      setAiSuggestions(suggestions);
    } catch (error) {
      console.error('获取AI建议失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 应用AI建议
  const applyAiSuggestion = (suggestion: {
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }) => {
    setResolutionStrategy(suggestion.strategy);
    if (suggestion.mergedData) {
      setCustomResolution(suggestion.mergedData);
    }
  };

  // 提交AI反馈
  const submitAiFeedback = (wasHelpful: boolean, comment?: string) => {
    conflictResolutionService.provideAIFeedback(
      conflict.id,
      wasHelpful,
      resolutionStrategy,
      comment
    );
    setFeedbackSubmitted(true);
  };

  // 渲染操作数据
  const renderOperationData = (operation: Operation) => {
    return (
      <Card className="operation-data-card">
        <Title level={5}>{getOperationTypeName(operation.type, t)}</Title>
        <Paragraph>{getOperationDescription(operation, t)}</Paragraph>
        <Divider />
        <JsonView data={operation.data} />
      </Card>
    );
  };

  // 渲染合并预览
  const renderMergePreview = () => {
    // 根据冲突类型生成合并预览
    let mergePreview: any = null;

    switch (conflict.type) {
      case ConflictType.ENTITY_CONFLICT:
      case ConflictType.COMPONENT_CONFLICT:
        mergePreview = renderPropertyMergePreview();
        break;

      case ConflictType.PROPERTY_CONFLICT:
        mergePreview = renderPropertyMergePreview();
        break;

      case ConflictType.SCENE_CONFLICT:
        mergePreview = renderSceneMergePreview();
        break;

      case ConflictType.DELETION_CONFLICT:
        mergePreview = renderDeletionConflictPreview();
        break;

      default:
        mergePreview = (
          <Alert
            message={t('collaboration.conflict.noPreview', '无法预览')}
            description={t('collaboration.conflict.noPreviewDescription', '此类型的冲突无法自动合并，请选择其他解决方案。')}
            type="warning"
            showIcon
          />
        );
        break;
    }

    return (
      <div className="merge-preview">
        <Alert
          message={t('collaboration.conflict.mergePreview', '合并预览')}
          description={t('collaboration.conflict.mergePreviewDescription', '系统将尝试合并两个版本的更改。')}
          type="info"
          showIcon
        />

        <div style={{ marginTop: 16 }}>
          {mergePreview}
        </div>
      </div>
    );
  };

  // 渲染属性合并预览
  const renderPropertyMergePreview = () => {
    const localProps = conflict.localOperation.data?.properties || {};
    const remoteProps = conflict.remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 找出冲突的属性（两边都修改了的属性）
    const conflictProps: string[] = [];
    for (const key in localProps) {
      if (key in remoteProps && JSON.stringify(localProps[key]) !== JSON.stringify(remoteProps[key])) {
        conflictProps.push(key);
      }
    }

    return (
      <div>
        <Table
          dataSource={Object.keys(mergedProps).map(key => ({
            key,
            property: key,
            localValue: JSON.stringify(localProps[key] !== undefined ? localProps[key] : '未修改'),
            remoteValue: JSON.stringify(remoteProps[key] !== undefined ? remoteProps[key] : '未修改'),
            mergedValue: JSON.stringify(mergedProps[key]),
            conflict: conflictProps.includes(key)
          }))}
          columns={[
            {
              title: t('collaboration.conflict.property', '属性'),
              dataIndex: 'property',
              key: 'property',
              render: (text, record: any) => (
                <span>
                  {text}
                  {record.conflict && (
                    <Tag color="red" style={{ marginLeft: 8 }}>
                      {t('collaboration.conflict.conflictTag', '冲突')}
                    </Tag>
                  )}
                </span>
              )
            },
            {
              title: t('collaboration.conflict.localValue', '本地值'),
              dataIndex: 'localValue',
              key: 'localValue'},
            {
              title: t('collaboration.conflict.remoteValue', '远程值'),
              dataIndex: 'remoteValue',
              key: 'remoteValue'},
            {
              title: t('collaboration.conflict.mergedResult', '合并结果'),
              dataIndex: 'mergedValue',
              key: 'mergedValue',
              render: (text, record: any) => (
                <span style={{ color: record.conflict ? 'red' : 'green' }}>
                  {text}
                </span>
              )
            }
          ]}
          size="small"
          pagination={false}
        />

        {conflictProps.length > 0 && (
          <Alert
            message={t('common.warning', '警告')}
            description={t('collaboration.conflict.mergeWarning', '存在无法自动合并的属性，合并后将使用本地值覆盖远程值。')}
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  // 渲染场景合并预览
  const renderSceneMergePreview = () => {
    const localProps = conflict.localOperation.data?.properties || {};
    const remoteProps = conflict.remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 找出冲突的属性（两边都修改了的属性）
    const conflictProps: string[] = [];
    for (const key in localProps) {
      if (key in remoteProps && JSON.stringify(localProps[key]) !== JSON.stringify(remoteProps[key])) {
        conflictProps.push(key);
      }
    }

    return (
      <div>
        <Table
          dataSource={Object.keys(mergedProps).map(key => ({
            key,
            property: key,
            localValue: JSON.stringify(localProps[key] !== undefined ? localProps[key] : '未修改'),
            remoteValue: JSON.stringify(remoteProps[key] !== undefined ? remoteProps[key] : '未修改'),
            mergedValue: JSON.stringify(mergedProps[key]),
            conflict: conflictProps.includes(key)
          }))}
          columns={[
            {
              title: t('collaboration.conflict.sceneProperty', '场景属性'),
              dataIndex: 'property',
              key: 'property',
              render: (text, record: any) => (
                <span>
                  {text}
                  {record.conflict && (
                    <Tag color="red" style={{ marginLeft: 8 }}>
                      {t('collaboration.conflict.conflictTag', '冲突')}
                    </Tag>
                  )}
                </span>
              )
            },
            {
              title: t('collaboration.conflict.localValue', '本地值'),
              dataIndex: 'localValue',
              key: 'localValue'},
            {
              title: t('collaboration.conflict.remoteValue', '远程值'),
              dataIndex: 'remoteValue',
              key: 'remoteValue'},
            {
              title: t('collaboration.conflict.mergedResult', '合并结果'),
              dataIndex: 'mergedValue',
              key: 'mergedValue',
              render: (text, record: any) => (
                <span style={{ color: record.conflict ? 'red' : 'green' }}>
                  {text}
                </span>
              )
            }
          ]}
          size="small"
          pagination={false}
        />

        {conflictProps.length > 0 && (
          <Alert
            message={t('common.warning', '警告')}
            description={t('collaboration.conflict.sceneMergeWarning', '存在无法自动合并的场景属性，合并后将使用本地值覆盖远程值。')}
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  // 渲染删除冲突预览
  const renderDeletionConflictPreview = () => {
    const isLocalDelete = conflict.localOperation.type === OperationType.ENTITY_DELETE;
    const isRemoteDelete = conflict.remoteOperation.type === OperationType.ENTITY_DELETE;

    if (isLocalDelete && isRemoteDelete) {
      return (
        <Alert
          message={t('collaboration.conflict.bothDeleted', '双方都删除了同一实体')}
          description={t('collaboration.conflict.bothDeletedDescription', '两个操作都是删除操作，可以安全合并。')}
          type="success"
          showIcon
        />
      );
    }

    if (isLocalDelete) {
      return (
        <Alert
          message={t('collaboration.conflict.localDeleteConflict', '本地删除冲突')}
          description={t('collaboration.conflict.localDeleteDescription', '本地操作删除了实体，而远程操作尝试修改该实体。合并后将保留删除操作，远程修改将被忽略。')}
          type="warning"
          showIcon
        />
      );
    }

    if (isRemoteDelete) {
      return (
        <Alert
          message={t('collaboration.conflict.remoteDeleteConflict', '远程删除冲突')}
          description={t('collaboration.conflict.remoteDeleteDescription', '远程操作删除了实体，而本地操作尝试修改该实体。合并后将保留删除操作，本地修改将被忽略。')}
          type="warning"
          showIcon
        />
      );
    }

    return (
      <Alert
        message={t('collaboration.conflict.unknownDeleteConflict', '未知删除冲突')}
        description={t('collaboration.conflict.unknownDeleteDescription', '无法确定删除冲突的类型，请手动选择解决方案。')}
        type="error"
        showIcon
      />
    );
  };



  // 渲染AI建议
  const renderAiSuggestions = () => {
    if (!showAiSuggestions) {
      return (
        <Button
          type="primary"
          icon={<RobotOutlined />}
          onClick={loadAiSuggestions}
          loading={aiLoading}
        >
          {t('collaboration.aiResolver.getAISuggestions', '获取AI建议')}
        </Button>
      );
    }

    if (aiLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div>{t('collaboration.aiResolver.processing')}</div>
        </div>
      );
    }

    if (aiSuggestions.length === 0) {
      return (
        <Alert
          message={t('collaboration.aiResolver.noSuggestions', '无法获取AI建议')}
          description={t('collaboration.aiResolver.noSuggestionsDescription', 'AI无法为此冲突提供建议，请手动解决。')}
          type="warning"
          showIcon
        />
      );
    }

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Title level={5}>
            <RobotOutlined /> {t('collaboration.aiResolver.suggestions')}
          </Title>
          <Text>{t('collaboration.aiResolver.suggestionsDescription', 'AI已分析此冲突并提供以下解决建议：')}</Text>
        </div>

        {aiSuggestions.map((suggestion, index) => (
          <Card
            key={index}
            style={{ marginBottom: 8 }}
            size="small"
            title={
              <Space>
                <BulbOutlined />
                <span>{t('collaboration.aiResolver.suggestion', '建议')} {index + 1}</span>
                <Tag color={suggestion.confidence > 0.8 ? 'green' : suggestion.confidence > 0.5 ? 'blue' : 'orange'}>
                  {t('collaboration.aiResolver.confidence')}: {Math.round(suggestion.confidence * 100)}%
                </Tag>
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="small"
                onClick={() => applyAiSuggestion(suggestion)}
              >
                {t('collaboration.aiResolver.apply', '应用')}
              </Button>
            }
          >
            <div>
              <div><strong>{t('collaboration.aiResolver.strategy', '策略')}:</strong> {getStrategyName(suggestion.strategy)}</div>
              <div><strong>{t('collaboration.aiResolver.explanation')}:</strong> {suggestion.explanation}</div>
            </div>
          </Card>
        ))}

        {!feedbackSubmitted && (
          <div style={{ marginTop: 16 }}>
            <Divider>{t('collaboration.aiResolver.feedback', '反馈')}</Divider>
            <Text>{t('collaboration.aiResolver.feedbackQuestion', '这些AI建议对您有帮助吗？')}</Text>
            <div style={{ marginTop: 8 }}>
              <Space>
                <Button
                  icon={<LikeOutlined />}
                  onClick={() => submitAiFeedback(true)}
                >
                  {t('collaboration.aiResolver.helpful', '有帮助')}
                </Button>
                <Button
                  icon={<DislikeOutlined />}
                  onClick={() => submitAiFeedback(false)}
                >
                  {t('collaboration.aiResolver.notHelpful', '没帮助')}
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 获取策略名称
  const getStrategyName = (strategy: ConflictResolutionStrategy): string => {
    switch (strategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        return t('collaboration.conflict.acceptLocal');
      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        return t('collaboration.conflict.acceptRemote');
      case ConflictResolutionStrategy.MERGE:
        return t('collaboration.conflict.merge');
      case ConflictResolutionStrategy.CUSTOM:
        return t('collaboration.conflict.custom');
      default:
        return t('collaboration.conflict.unknownStrategy', '未知策略');
    }
  };

  return (
    <Modal
      title={
        <Space>
          <MergeCellsOutlined />
          <span>{t('collaboration.conflict.title', '冲突解决')}</span>
        </Space>
      }
      open={visible}
      width={800}
      onCancel={handleCancel}
      onOk={handleConfirm}
      closable={true}
      maskClosable={false}
      keyboard={true}
      destroyOnClose={true}
      centered={true}
      forceRender={false}
      getContainer={false}
      footer={[
        <Button
          key="cancel"
          onClick={handleCancel}
          disabled={loading}
        >
          {t('common.cancel', '取消')}
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={loading}
          onClick={handleConfirm}
          disabled={!conflict}
        >
          {t('collaboration.conflict.confirmResolve', '确认解决')}
        </Button>
      ]}
    >
      <div className="conflict-resolution-dialog">
        <Alert
          message={t('collaboration.conflict.detected')}
          description={t('collaboration.conflict.description')}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {(() => {
          const tabItems = [
            {
              key: 'compare',
              label: (
                <span>
                  <MergeCellsOutlined />
                  {t('collaboration.conflict.compareDifferences', '比较差异')}
                </span>
              ),
              children: (
                <Row gutter={16}>
                  <Col span={12}>
                    <Card
                      title={
                        <Space>
                          <UserOutlined />
                          <span>{t('collaboration.conflict.localVersion', '本地版本')}</span>
                        </Space>
                      }
                      className="version-card"
                      extra={
                        <Button
                          type="link"
                          size="small"
                          onClick={() => setResolutionStrategy(ConflictResolutionStrategy.ACCEPT_LOCAL)}
                        >
                          {t('collaboration.conflict.useThisVersion', '采用此版本')}
                        </Button>
                      }
                    >
                      {renderOperationData(conflict.localOperation)}
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card
                      title={
                        <Space>
                          <CloudOutlined />
                          <span>{t('collaboration.conflict.remoteVersion', '远程版本')}</span>
                        </Space>
                      }
                      className="version-card"
                      extra={
                        <Button
                          type="link"
                          size="small"
                          onClick={() => setResolutionStrategy(ConflictResolutionStrategy.ACCEPT_REMOTE)}
                        >
                          {t('collaboration.conflict.useThisVersion', '采用此版本')}
                        </Button>
                      }
                    >
                      {renderOperationData(conflict.remoteOperation)}
                    </Card>
                  </Col>
                </Row>
              )
            },
            {
              key: 'ai',
              label: (
                <span>
                  <RobotOutlined />
                  {t('collaboration.aiResolver.suggestions')}
                </span>
              ),
              children: (
                <div style={{ padding: '16px' }}>
                  {renderAiSuggestions()}
                </div>
              )
            },
            {
              key: 'resolution',
              label: (
                <span>
                  <CheckCircleOutlined />
                  {t('collaboration.conflict.resolutionOptions', '解决方案')}
                </span>
              ),
              children: (
                <div className="resolution-options">
                  <Title level={5}>{t('collaboration.conflict.selectStrategy', '选择解决策略')}</Title>

                  <Radio.Group
                    value={resolutionStrategy}
                    onChange={handleStrategyChange}
                  >
                    <Space direction="vertical">
                      <Radio value={ConflictResolutionStrategy.ACCEPT_LOCAL}>
                        <Space>
                          <UserOutlined />
                          <span>{t('collaboration.conflict.acceptLocal')}</span>
                        </Space>
                        <div className="option-description">
                          {t('collaboration.conflict.acceptLocalDescription', '保留您的修改，丢弃远程修改。')}
                        </div>
                      </Radio>
                      <Radio value={ConflictResolutionStrategy.ACCEPT_REMOTE}>
                        <Space>
                          <CloudOutlined />
                          <span>{t('collaboration.conflict.acceptRemote')}</span>
                        </Space>
                        <div className="option-description">
                          {t('collaboration.conflict.acceptRemoteDescription', '保留远程修改，丢弃您的修改。')}
                        </div>
                      </Radio>
                      <Radio value={ConflictResolutionStrategy.MERGE}>
                        <Space>
                          <MergeCellsOutlined />
                          <span>{t('collaboration.conflict.merge')}</span>
                        </Space>
                        <div className="option-description">
                          {t('collaboration.conflict.mergeDescription', '尝试自动合并两个版本的修改。')}
                        </div>
                      </Radio>
                      <Radio value={ConflictResolutionStrategy.CUSTOM}>
                        <Space>
                          <EditOutlined />
                          <span>{t('collaboration.conflict.custom')}</span>
                        </Space>
                        <div className="option-description">
                          {t('collaboration.conflict.customDescription', '手动编辑解决方案。')}
                        </div>
                      </Radio>
                    </Space>
                  </Radio.Group>

                  {resolutionStrategy === ConflictResolutionStrategy.MERGE && (
                    <div className="merge-preview" style={{ marginTop: 16 }}>
                      {renderMergePreview()}
                    </div>
                  )}

                  {resolutionStrategy === ConflictResolutionStrategy.CUSTOM && (
                    <div className="custom-resolution" style={{ marginTop: 16 }}>
                      <Title level={5}>{t('collaboration.conflict.customResolution', '自定义解决方案')}</Title>
                      <Alert
                        message={t('common.info', '提示')}
                        description={t('collaboration.conflict.customResolutionDescription', '您可以手动编辑数据来解决冲突。')}
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />

                      <Form layout="vertical">
                        <Form.Item label={t('collaboration.conflict.customData', '自定义数据')}>
                          <Input.TextArea
                            rows={6}
                            defaultValue={JSON.stringify(conflict.localOperation.data, null, 2)}
                            onChange={(e) => {
                              try {
                                setCustomResolution(JSON.parse(e.target.value));
                              } catch (error) {
                                console.error('JSON解析错误:', error);
                              }
                            }}
                          />
                        </Form.Item>
                      </Form>
                    </div>
                  )}
                </div>
              )
            }
          ];

          return (
            <Tabs
              defaultActiveKey="compare"
              items={tabItems}
            />
          );
        })()}
      </div>
    </Modal>
  );
};

export default ConflictResolutionDialog;
